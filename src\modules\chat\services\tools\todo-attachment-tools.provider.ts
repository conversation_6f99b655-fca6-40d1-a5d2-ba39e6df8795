import { Injectable } from '@nestjs/common';
import { TodoAttachmentService } from '@modules/todolists/services/todo-attachment.service';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { SortDirection } from '@/common/dto/query.dto';

/**
 * Todo Attachment Tools Provider
 * Cung cấp các tools liên quan đến quản lý tệp đính kèm công việc
 */
@Injectable()
export class TodoAttachmentToolsProvider {
  constructor(private readonly todoAttachmentService: TodoAttachmentService) {}

  /**
   * L<PERSON>y tất cả todo attachment tools
   */
  getTools() {
    return [
      // Tạo URL upload cho tệp đính kèm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createUploadDto = {
              fileName: _args.fileName,
              fileSize: _args.fileSize,
              mimeType: _args.mimeType,
              todoId: _args.todoId,
            };

            const uploadInfo = await this.todoAttachmentService.createUploadUrl(tenantId, userId, createUploadDto);
            return `URL upload đã được tạo thành công:\n${JSON.stringify(uploadInfo, null, 2)}`;
          } catch (error) {
            return `Tạo URL upload thất bại: ${error.message}`;
          }
        },
        {
          name: 'create_attachment_upload_url',
          description: 'Tạo presigned URL để upload tệp đính kèm cho công việc',
          schema: z.object({
            fileName: z.string().describe('Tên tệp'),
            fileSize: z.number().describe('Kích thước tệp (bytes)'),
            mimeType: z.string().describe('Loại MIME của tệp'),
            todoId: z.number().describe('ID công việc'),
          }),
        }
      ),

      // Xác nhận upload thành công
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const confirmUploadDto = {
              s3Key: _args.s3Key,
              todoId: _args.todoId,
              fileName: _args.fileName,
              size: _args.size,
              contentType: _args.contentType,
            };

            const attachment = await this.todoAttachmentService.confirmUpload(tenantId, userId, confirmUploadDto);
            return `Upload đã được xác nhận thành công:\n${JSON.stringify(attachment, null, 2)}`;
          } catch (error) {
            return `Xác nhận upload thất bại: ${error.message}`;
          }
        },
        {
          name: 'confirm_attachment_upload',
          description: 'Xác nhận upload thành công và lưu thông tin tệp đính kèm',
          schema: z.object({
            s3Key: z.string().describe('Key của file trên S3/Cloud Storage'),
            todoId: z.number().describe('ID công việc'),
            fileName: z.string().describe('Tên tệp'),
            size: z.number().optional().describe('Kích thước tệp thực tế (bytes)'),
            contentType: z.string().optional().describe('Loại MIME của tệp'),
          }),
        }
      ),

      // Lấy danh sách tệp đính kèm của công việc
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const attachments = await this.todoAttachmentService.findByTodoId(tenantId, _args.todoId);
            return `Tìm thấy ${attachments.length} tệp đính kèm cho công việc:\n${JSON.stringify(attachments, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_todo_attachments',
          description: 'Lấy danh sách tệp đính kèm của một công việc',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
          }),
        }
      ),

      // Lấy danh sách tất cả tệp đính kèm (có phân trang)
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              page: _args.page || 1,
              limit: _args.limit || 20,
              todoId: _args.todoId,
              createdBy: _args.createdBy,
              search: _args.search,
              sortBy: _args.sortBy,
              sortDirection: _args.sortDirection as SortDirection,
            };

            const result = await this.todoAttachmentService.findAll(tenantId, query);
            return `Tìm thấy ${result.items.length} tệp đính kèm (tổng ${result.meta.totalItems}):\n${JSON.stringify(result.items, null, 2)}`;
          } catch (error) {
            return `Lấy danh sách tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_all_attachments_paginated',
          description: 'Lấy danh sách tất cả tệp đính kèm với phân trang và bộ lọc',
          schema: z.object({
            page: z.number().optional().default(1).describe('Số trang'),
            limit: z.number().optional().default(20).describe('Số lượng kết quả trên mỗi trang'),
            todoId: z.number().optional().describe('Lọc theo ID công việc'),
            createdBy: z.number().optional().describe('Lọc theo ID người tạo'),
            search: z.string().optional().describe('Tìm kiếm theo tên tệp'),
            sortBy: z.string().optional().default('createdAt').describe('Trường sắp xếp'),
            sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC').describe('Hướng sắp xếp'),
          }),
        }
      ),

      // Lấy TẤT CẢ danh sách tệp đính kèm (KHÔNG phân trang) - API MỚI
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const query = {
              todoId: _args.todoId,
              createdBy: _args.createdBy,
              search: _args.search,
              sortBy: _args.sortBy || 'createdAt',
              sortDirection: (_args.sortDirection || 'DESC') as SortDirection,
            };

            const allAttachments = await this.todoAttachmentService.findAllWithoutPagination(tenantId, query);
            return `Lấy tất cả ${allAttachments.length} tệp đính kèm thành công (không phân trang):\n${JSON.stringify(allAttachments, null, 2)}`;
          } catch (error) {
            return `Lấy tất cả danh sách tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_all_attachments_no_pagination',
          description: 'Lấy TẤT CẢ danh sách tệp đính kèm không phân trang - hữu ích cho export, dropdown, thống kê tổng quan',
          schema: z.object({
            todoId: z.number().optional().describe('Lọc theo ID công việc'),
            createdBy: z.number().optional().describe('Lọc theo ID người tạo'),
            search: z.string().optional().describe('Tìm kiếm theo tên tệp'),
            sortBy: z.string().optional().default('createdAt').describe('Trường sắp xếp (createdAt, filename, size)'),
            sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC').describe('Hướng sắp xếp'),
          }),
        }
      ),

      // Lấy chi tiết tệp đính kèm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');

            const attachment = await this.todoAttachmentService.findById(tenantId, _args.attachmentId);
            return `Chi tiết tệp đính kèm:\n${JSON.stringify(attachment, null, 2)}`;
          } catch (error) {
            return `Lấy chi tiết tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'get_attachment_details',
          description: 'Lấy chi tiết tệp đính kèm theo ID',
          schema: z.object({
            attachmentId: z.number().describe('ID tệp đính kèm'),
          }),
        }
      ),

      // Xóa tệp đính kèm
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            await this.todoAttachmentService.removeAttachment(tenantId, userId, _args.attachmentId);
            return `Tệp đính kèm đã được xóa thành công`;
          } catch (error) {
            return `Xóa tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'delete_attachment',
          description: 'Xóa tệp đính kèm',
          schema: z.object({
            attachmentId: z.number().describe('ID tệp đính kèm'),
          }),
        }
      ),

      // Thêm tệp đính kèm (Legacy method)
      tool(
        async (
          _args,
          config: ToolRunnableConfig,
        ): Promise<string> => {
          try {
            const tenantId = parseInt(config?.configurable?.['tenantId'] || '0');
            const userId = parseInt(config?.configurable?.['userId'] || '0');

            const createDto = {
              todoId: _args.todoId,
              filename: _args.fileName,
              url: _args.fileUrl,
              size: _args.fileSize,
              contentType: _args.mimeType,
            };

            const attachment = await this.todoAttachmentService.addAttachment(tenantId, userId, createDto);
            return `Tệp đính kèm đã được thêm thành công:\n${JSON.stringify(attachment, null, 2)}`;
          } catch (error) {
            return `Thêm tệp đính kèm thất bại: ${error.message}`;
          }
        },
        {
          name: 'add_attachment_legacy',
          description: 'Thêm tệp đính kèm cho công việc (phương pháp cũ)',
          schema: z.object({
            todoId: z.number().describe('ID công việc'),
            fileName: z.string().describe('Tên tệp'),
            fileUrl: z.string().describe('URL tệp'),
            fileSize: z.number().describe('Kích thước tệp (bytes)'),
            mimeType: z.string().describe('Loại MIME của tệp'),
          }),
        }
      ),
    ];
  }
}
